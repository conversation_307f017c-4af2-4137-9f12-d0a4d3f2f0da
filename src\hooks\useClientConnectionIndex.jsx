import { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useSession } from 'next-auth/react';
import { useCommonAPIs } from './useCommonAPIs';
import { resetCreateConnectionAtConnectionIndex } from '@/redux/actions/createConnectionAtConnectionIndex';
import { actions, FOUND, NOT_FOUND } from '@/containers/commonConstants';
import useNotification from './useNotification';
import { useTranslation } from 'react-i18next';

/**
 * Custom hook to handle client index and connection index logic consistently
 * across different components (demographic flow, save for later, save response)
 */
export const useClientConnectionIndex = ({
  demographicFields,
  selfRegistration,
  onSuccess,
  onClientNotFound,
  onError,
}) => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [, sendNotification] = useNotification();
  const { data: authSession } = useSession();
  
  const [isClientSearched, setIsClientSearched] = useState(false);
  const [isClientFound, setIsClientFound] = useState(null);

  const {
    fetchSearchIndividualAtClientIndex,
    makeConnectionAtConnectionIndex,
    getClientId,
  } = useCommonAPIs();

  const {
    isSearchIndividualAtClientIndexFetching,
    searchIndividualAtClientIndexSuccessData,
    searchIndividualAtClientIndexError,
    searchIndividualAtClientIndexErrorData,
  } = useSelector((state) => state.searchIndividualAtClientIndexReducer);

  const {
    isCreateConnectionAtConnectionIndexFetching,
    createConnectionAtConnectionIndexSuccess,
    createConnectionAtConnectionIndexErrorData,
  } = useSelector((state) => state.createConnectionAtConnectionIndexReducer);

  const {
    individualUserInfoSuccessData,
  } = useSelector((state) => state.getIndividualUserInfoReducer);

  const {
    checkExistingConnectionIndexError,
  } = useSelector((state) => state.checkExistingConnectionIndexReducer);

  // Handle successful client index search
  useEffect(() => {
    if (isClientSearched && searchIndividualAtClientIndexSuccessData) {
      const { found, identifiers, clientId } = searchIndividualAtClientIndexSuccessData?.data || {};
      setIsClientFound(found ? FOUND : NOT_FOUND);

      if (!found) {
        if (onClientNotFound) {
          onClientNotFound();
        }
        return;
      }

      if (individualUserInfoSuccessData && checkExistingConnectionIndexError) {
        makeConnectionAtConnectionIndex(clientId);
      } else {
        if (onSuccess) {
          onSuccess({
            ...demographicFields,
            clientIdentifiers: identifiers,
          });
        }
        setIsClientSearched(false);
      }
    }
  }, [
    searchIndividualAtClientIndexSuccessData,
    individualUserInfoSuccessData,
    checkExistingConnectionIndexError,
    isClientSearched,
    demographicFields,
    onSuccess,
    onClientNotFound,
  ]);

  // Handle client index search errors
  useEffect(() => {
    if (searchIndividualAtClientIndexError && searchIndividualAtClientIndexErrorData) {
      setIsClientSearched(false);
      if (
        searchIndividualAtClientIndexErrorData.statusCode === 400 ||
        searchIndividualAtClientIndexErrorData.statusCode === 500
      ) {
        let errorMessage = t('apiError');

        if (
          searchIndividualAtClientIndexErrorData.errorDetails &&
          searchIndividualAtClientIndexErrorData.errorDetails.length > 0
        ) {
          const firstError = searchIndividualAtClientIndexErrorData.errorDetails[0];
          if (firstError.errorDetails && firstError.errorDetails.length > 0) {
            errorMessage = firstError.errorDetails[0].message || firstError.message || errorMessage;
          } else {
            errorMessage = firstError.message || errorMessage;
          }
        }

        if (onError) {
          onError(errorMessage);
        } else {
          sendNotification({ variant: 'error', msg: errorMessage });
        }
      } else {
        const errorMessage = t('apiError');
        if (onError) {
          onError(errorMessage);
        } else {
          sendNotification({ variant: 'error', msg: errorMessage });
        }
      }
    }
  }, [searchIndividualAtClientIndexError, searchIndividualAtClientIndexErrorData, onError, t]);

  // Handle connection index creation success
  useEffect(() => {
    if (createConnectionAtConnectionIndexSuccess) {
      setIsClientSearched(false);
      dispatch(resetCreateConnectionAtConnectionIndex());
      if (onSuccess) {
        onSuccess(demographicFields);
      }
    }
    if (createConnectionAtConnectionIndexErrorData?.message?.includes('Active connection')) {
      setIsClientSearched(false);
      dispatch(resetCreateConnectionAtConnectionIndex());
      if (onSuccess) {
        onSuccess(demographicFields);
      }
    }
  }, [createConnectionAtConnectionIndexSuccess, createConnectionAtConnectionIndexErrorData, demographicFields, onSuccess]);

  // Function to trigger client index search
  const searchClientIndex = () => {
    if (demographicFields && authSession?.user?.cambianId) {
      fetchSearchIndividualAtClientIndex(demographicFields, selfRegistration);
      setIsClientSearched(true);
    }
  };

  // Function to reset state
  const resetState = () => {
    setIsClientSearched(false);
    setIsClientFound(null);
  };

  return {
    isClientSearched,
    isClientFound,
    isSearchIndividualAtClientIndexFetching,
    isCreateConnectionAtConnectionIndexFetching,
    searchClientIndex,
    resetState,
  };
};
